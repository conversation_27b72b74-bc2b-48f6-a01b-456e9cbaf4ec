#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 sh603194 在 2025年6月16日派现金事件的复权计算
"""

import sys
import pandas as pd
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_sh603194_dividend():
    """
    测试 sh603194 在 20250616 的派现金事件
    
    用户观察到的数据：
    日期        不复权    前复权    后复权
    20250613   42.75     42.12     42.75
    20250616   42.19     42.19     42.82
    20250617   42.8      42.8      43.43
    """
    print("=" * 60)
    print("测试 sh603194 在 20250616 的派现金事件")
    print("=" * 60)
    
    try:
        from quant.tdx_data import get_code, GbbqReader
        
        # 获取复权数据
        print("正在获取复权数据...")
        gbbq = GbbqReader().get_data()
        
        # 查找 sh603194 的复权数据
        stock_code = '603194'
        stock_gbbq = gbbq[gbbq['code'] == stock_code].copy()
        
        print(f"找到 {stock_code} 的复权数据 {len(stock_gbbq)} 条")
        
        if len(stock_gbbq) > 0:
            print("\n复权事件列表：")
            for _, row in stock_gbbq.iterrows():
                print(f"  日期:{row['datetime']} 类别:{row['category']} "
                      f"数据1:{row['hongli_panqianliutong']} "
                      f"数据2:{row['peigujia_qianzongguben']} "
                      f"数据3:{row['songgu_qianzongguben']} "
                      f"数据4:{row['peigu_houzongguben']}")
        
        # 查找 20250616 的除权除息事件
        dividend_event = stock_gbbq[stock_gbbq['datetime'] == 20250616]
        
        if len(dividend_event) > 0:
            print(f"\n找到 20250616 的除权除息事件：")
            event = dividend_event.iloc[0]
            print(f"  类别: {event['category']}")
            print(f"  分红(每10股派几元): {event['hongli_panqianliutong']}")
            print(f"  配股价: {event['peigujia_qianzongguben']}")
            print(f"  送转股(每10股送几股): {event['songgu_qianzongguben']}")
            print(f"  配股(每10股配几股): {event['peigu_houzongguben']}")
            
            # 计算复权因子
            if event['category'] == 1:  # 除权除息
                cash_dividend = event['hongli_panqianliutong'] / 10.0  # 每股现金红利
                bonus_ratio = event['songgu_qianzongguben'] / 10.0    # 送转股比例
                rights_ratio = event['peigu_houzongguben'] / 10.0     # 配股比例
                rights_price = event['peigujia_qianzongguben']        # 配股价格
                
                print(f"\n复权参数：")
                print(f"  每股现金红利: {cash_dividend} 元")
                print(f"  送转股比例: {bonus_ratio}")
                print(f"  配股比例: {rights_ratio}")
                print(f"  配股价格: {rights_price} 元")
        else:
            print(f"\n未找到 20250616 的除权除息事件")
        
        # 获取股票数据
        print(f"\n正在获取 sh603194 的股票数据...")
        
        # 获取不复权数据
        df_no_adj = get_code('sh603194', fq=0, begin=20250610, end=20250620)
        print(f"不复权数据：{len(df_no_adj)} 条")
        
        # 获取前复权数据
        df_forward = get_code('sh603194', fq=1, begin=20250610, end=20250620)
        print(f"前复权数据：{len(df_forward)} 条")
        
        # 获取后复权数据
        df_backward = get_code('sh603194', fq=2, begin=20250610, end=20250620)
        print(f"后复权数据：{len(df_backward)} 条")
        
        # 显示关键日期的数据
        print(f"\n关键日期数据对比：")
        print("日期        不复权收盘  前复权收盘  后复权收盘")
        print("-" * 50)
        
        target_dates = [20250613, 20250616, 20250617]
        
        for date in target_dates:
            # 查找对应日期的数据
            no_adj_row = df_no_adj[df_no_adj['datetime'] == date]
            forward_row = df_forward[df_forward['datetime'] == date]
            backward_row = df_backward[df_backward['datetime'] == date]
            
            if len(no_adj_row) > 0 and len(forward_row) > 0 and len(backward_row) > 0:
                no_adj_close = no_adj_row.iloc[0]['close']
                forward_close = forward_row.iloc[0]['close']
                backward_close = backward_row.iloc[0]['close']
                
                print(f"{date}    {no_adj_close:8.2f}    {forward_close:8.2f}    {backward_close:8.2f}")
            else:
                print(f"{date}    数据缺失")
        
        # 与用户观察数据对比
        print(f"\n用户观察数据对比：")
        print("日期        用户观察(不复权)  计算结果(不复权)  差异")
        print("-" * 55)
        
        user_data = {
            20250613: 42.75,
            20250616: 42.19,
            20250617: 42.8
        }
        
        for date, user_close in user_data.items():
            no_adj_row = df_no_adj[df_no_adj['datetime'] == date]
            if len(no_adj_row) > 0:
                calc_close = no_adj_row.iloc[0]['close']
                diff = abs(user_close - calc_close)
                print(f"{date}        {user_close:8.2f}          {calc_close:8.2f}      {diff:6.2f}")
            else:
                print(f"{date}        {user_close:8.2f}          数据缺失")
        
        print(f"\n用户观察数据对比（前复权）：")
        print("日期        用户观察(前复权)  计算结果(前复权)  差异")
        print("-" * 55)
        
        user_forward_data = {
            20250613: 42.12,
            20250616: 42.19,
            20250617: 42.8
        }
        
        for date, user_close in user_forward_data.items():
            forward_row = df_forward[df_forward['datetime'] == date]
            if len(forward_row) > 0:
                calc_close = forward_row.iloc[0]['close']
                diff = abs(user_close - calc_close)
                print(f"{date}        {user_close:8.2f}          {calc_close:8.2f}      {diff:6.2f}")
            else:
                print(f"{date}        {user_close:8.2f}          数据缺失")
        
        print(f"\n用户观察数据对比（后复权）：")
        print("日期        用户观察(后复权)  计算结果(后复权)  差异")
        print("-" * 55)
        
        user_backward_data = {
            20250613: 42.75,
            20250616: 42.82,
            20250617: 43.43
        }
        
        for date, user_close in user_backward_data.items():
            backward_row = df_backward[df_backward['datetime'] == date]
            if len(backward_row) > 0:
                calc_close = backward_row.iloc[0]['close']
                diff = abs(user_close - calc_close)
                print(f"{date}        {user_close:8.2f}          {calc_close:8.2f}      {diff:6.2f}")
            else:
                print(f"{date}        {user_close:8.2f}          数据缺失")
        
        # 手动计算复权因子验证
        if len(dividend_event) > 0:
            event = dividend_event.iloc[0]
            if event['category'] == 1:
                print(f"\n手动计算复权因子验证：")
                
                # 获取除权前一天的收盘价（20250613）
                pre_div_row = df_no_adj[df_no_adj['datetime'] == 20250613]
                if len(pre_div_row) > 0:
                    pre_close = pre_div_row.iloc[0]['close']
                    print(f"除权前收盘价（20250613）: {pre_close}")
                    
                    # 计算复权参数
                    cash_dividend = event['hongli_panqianliutong'] / 10.0
                    bonus_ratio = event['songgu_qianzongguben'] / 10.0
                    rights_ratio = event['peigu_houzongguben'] / 10.0
                    rights_price = event['peigujia_qianzongguben']
                    
                    # 计算理论除权价
                    theoretical_ex_price = (pre_close - cash_dividend + rights_ratio * rights_price) / (1 + bonus_ratio + rights_ratio)
                    print(f"理论除权价: {theoretical_ex_price:.4f}")
                    
                    # 计算前复权因子
                    forward_factor = theoretical_ex_price / pre_close
                    print(f"前复权因子: {forward_factor:.6f}")
                    
                    # 计算后复权因子
                    backward_factor = pre_close / theoretical_ex_price
                    print(f"后复权因子: {backward_factor:.6f}")
                    
                    # 验证前复权价格
                    print(f"\n前复权价格验证：")
                    print(f"20250613 前复权价格应为: {pre_close * forward_factor:.2f}")
                    
                    # 验证后复权价格
                    print(f"\n后复权价格验证：")
                    div_day_row = df_no_adj[df_no_adj['datetime'] == 20250616]
                    if len(div_day_row) > 0:
                        div_day_close = div_day_row.iloc[0]['close']
                        print(f"20250616 后复权价格应为: {div_day_close * backward_factor:.2f}")
                    
                    next_day_row = df_no_adj[df_no_adj['datetime'] == 20250617]
                    if len(next_day_row) > 0:
                        next_day_close = next_day_row.iloc[0]['close']
                        print(f"20250617 后复权价格应为: {next_day_close * backward_factor:.2f}")
        
    except Exception as e:
        print(f"测试时出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sh603194_dividend()
