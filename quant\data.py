"""
数据获取
"""
import akshare as ak
import pandas as pd
import datetime, time
from bs4 import BeautifulSoup
import requests

from quant.config import CONFIG

def _fund_hist_data(code: str, start_date: str="19900101", end_date: str="20500101", _type:str = None, adjust='qfq', k_type = 'daily',his=True) -> pd.DataFrame:
    """
    获取基金历史数据
    his: 是否只获取历史数据(排除盘中数据)
    k_type: 'daily' | 'min'/'1' | '5min'/'5' | '15min'/'15' | '30min'/'30' | '60min'/'60'
    """
    if None is _type:
        _type = 'etf' if code in CONFIG.ETFS else 'lof'

    # 规范k_type
    if k_type == 'min':
        k_type = '1'
    elif k_type in ['5min', '15min', '30min', '60min']:
        k_type = k_type.replace('min', '')

    if _type == 'etf':
        if k_type == 'daily':
            data = ak.fund_etf_hist_em(symbol=code, period="daily", start_date=start_date, end_date=end_date, adjust=adjust)
        else:
            data = ak.fund_etf_hist_min_em(symbol=code, period=k_type, start_date=start_date, end_date=end_date, adjust=adjust)
    elif _type == 'lof':
        if k_type == 'daily':
            data = ak.fund_lof_hist_em(symbol=code, period="daily", start_date=start_date, end_date=end_date, adjust=adjust)
        else:
            data = ak.fund_lof_hist_min_em(symbol=code, period=k_type, start_date=start_date, end_date=end_date, adjust=adjust)

    if his:
        # 过滤盘中数据
        dt = datetime.datetime.now()
        # 9：00 - 15：00
        in_trade = str(dt.date()) in CONFIG.TRADE_DATES and dt.hour < 15
        if in_trade:
            if k_type == 'daily':# 只对日线数据过滤
                data = data.loc[data['日期'] < dt.strftime('%Y-%m-%d'), :]

    if k_type == 'daily':
        data = data.rename(columns={'日期': '时间'})
    
    data['时间'] = pd.to_datetime(data['时间'])
    return data

def _etf_realtime_tick_data() -> pd.DataFrame:
    """
    获取全部ETF实时分时数据
    """
    data = ak.fund_etf_spot_em()
    return data

def _lof_realtime_tick_data() -> pd.DataFrame:
    """
    获取全部LOF实时分时数据
    """
    data = ak.fund_lof_spot_em()
    return data

def fund_premium_rate():
    """
    获取 集思录 qdii基金的溢价率
    """
    url = f'https://www.jisilu.cn/data/qdii/qdii_list/E?___jsl=LST___t={int(time.time()*1000)}&rp=22&page=1'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    response = requests.get(url, headers=headers)
    # 解析JSON响应
    data = response.json()

    # 提取数据
    fund_data = []
    for row in data['rows']:
        fund_info = row['cell']
        fund_data.append({
            'fund_id': fund_info['fund_id'],
            'fund_name': fund_info['fund_nm'],
            'discount_rt': fund_info['discount_rt'],
            'index_nm': fund_info['index_nm'],
            'date': fund_info['nav_dt']
        })

    # 转换为DataFrame
    df = pd.DataFrame(fund_data)
    return df

def fund_hist_data(codes: str|list[str], start_date: str="19900101", end_date: str="20500101", _type:str = None, adjust='qfq', his=True) -> pd.DataFrame:
    """
    获取基金历史日数据
    """
    if isinstance(codes, str):
        return _fund_hist_data(codes, start_date, end_date, _type, adjust, 'daily', his)
    data = pd.DataFrame()
    for code in codes:
        _d = _fund_hist_data(code, start_date, end_date, _type, adjust, 'daily', his)
        _d['code'] = code
        data = pd.concat([data, _d], ignore_index=True)
    return data

def fund_hist_min_data(codes: str|list[str], start_date: str="19900101", end_date: str="20500101", _type:str = None, adjust='qfq', his=True, k_type:str = 'min') -> pd.DataFrame:
    """
    获取基金历史分钟数据
    k_type: 'min'/'1' | '5min'/'5' | '15min'/'15' | '30min'/'30' | '60min'/'60'
    """
    if isinstance(codes, str):
        return _fund_hist_data(codes, start_date, end_date, _type, adjust, k_type, his)
    data = pd.DataFrame()
    for code in codes:
        _d = _fund_hist_data(code, start_date, end_date, _type, adjust, k_type, his)
        _d['code'] = code
        data = pd.concat([data, _d], ignore_index=True)
    return data

def fund_realtime_tick_data(_type:str = 'etf') -> pd.DataFrame:
    """
    获取全部基金实时分时数据
    _type: 'etf' | 'lof'
    """
    if _type == 'etf':
        data = _etf_realtime_tick_data()
    elif _type == 'lof':
        data = _lof_realtime_tick_data()
    return data

if __name__ == "__main__":
    # d = fund_hist_data('513110')
    # print(d)

    d = fund_hist_min_data('513110')
    print(d)
