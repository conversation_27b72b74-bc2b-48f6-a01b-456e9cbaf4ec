#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复权计算是否正确
"""

import sys
import pandas as pd
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_fuquan_formula():
    """
    测试复权公式的正确性
    使用一个简单的例子来验证
    """
    print("=" * 60)
    print("测试复权公式的正确性")
    print("=" * 60)
    
    # 假设一个简单的除权除息案例
    # 除权前收盘价：10元
    # 每10股派现金红利：2元 (即每股0.2元)
    # 每10股送股：3股 (即每股送0.3股)
    # 每10股配股：1股，配股价5元 (即每股配0.1股，配股价5元)
    
    pre_close = 10.0
    cash_dividend = 2.0 / 10.0  # 每股现金红利 = 0.2元
    bonus_ratio = 3.0 / 10.0    # 送股比例 = 0.3
    rights_ratio = 1.0 / 10.0   # 配股比例 = 0.1
    rights_price = 5.0          # 配股价格 = 5元
    
    print(f"测试案例：")
    print(f"  除权前收盘价：{pre_close}元")
    print(f"  每股现金红利：{cash_dividend}元")
    print(f"  送股比例：{bonus_ratio} (每股送{bonus_ratio}股)")
    print(f"  配股比例：{rights_ratio} (每股配{rights_ratio}股)")
    print(f"  配股价格：{rights_price}元")
    print()
    
    # 计算理论除权价
    # 除权价 = (除权前收盘价 - 现金红利 + 配股比例 × 配股价) / (1 + 送股比例 + 配股比例)
    theoretical_ex_price = (pre_close - cash_dividend + rights_ratio * rights_price) / (1 + bonus_ratio + rights_ratio)
    
    print(f"理论除权价计算：")
    print(f"  分子 = {pre_close} - {cash_dividend} + {rights_ratio} × {rights_price} = {pre_close - cash_dividend + rights_ratio * rights_price}")
    print(f"  分母 = 1 + {bonus_ratio} + {rights_ratio} = {1 + bonus_ratio + rights_ratio}")
    print(f"  理论除权价 = {theoretical_ex_price:.4f}元")
    print()
    
    # 计算前复权因子
    forward_factor = theoretical_ex_price / pre_close
    print(f"前复权因子 = 除权价 / 除权前价格 = {theoretical_ex_price:.4f} / {pre_close} = {forward_factor:.6f}")
    
    # 计算后复权因子  
    backward_factor = pre_close / theoretical_ex_price
    print(f"后复权因子 = 除权前价格 / 除权价 = {pre_close} / {theoretical_ex_price:.4f} = {backward_factor:.6f}")
    print()
    
    # 验证代码中的计算
    print("验证代码中的计算：")
    
    # 前复权计算（代码中的逻辑）
    numerator = pre_close - cash_dividend + rights_ratio * rights_price
    denominator = (1 + bonus_ratio + rights_ratio) * pre_close
    code_forward_factor = numerator / denominator
    
    print(f"代码前复权因子计算：")
    print(f"  分子 = {numerator}")
    print(f"  分母 = {denominator}")
    print(f"  前复权因子 = {code_forward_factor:.6f}")
    
    # 后复权计算（代码中的逻辑）
    code_backward_factor = denominator / numerator
    print(f"代码后复权因子计算：")
    print(f"  后复权因子 = {code_backward_factor:.6f}")
    print()
    
    # 比较结果
    print("结果比较：")
    print(f"  理论前复权因子：{forward_factor:.6f}")
    print(f"  代码前复权因子：{code_forward_factor:.6f}")
    print(f"  差异：{abs(forward_factor - code_forward_factor):.8f}")
    print()
    print(f"  理论后复权因子：{backward_factor:.6f}")
    print(f"  代码后复权因子：{code_backward_factor:.6f}")
    print(f"  差异：{abs(backward_factor - code_backward_factor):.8f}")
    print()
    
    # 验证前后复权因子的关系
    print("验证前后复权因子关系：")
    print(f"  前复权因子 × 后复权因子 = {forward_factor:.6f} × {backward_factor:.6f} = {forward_factor * backward_factor:.6f}")
    print(f"  应该等于 1.0，差异：{abs(forward_factor * backward_factor - 1.0):.8f}")
    
    return forward_factor, backward_factor, code_forward_factor, code_backward_factor

def test_real_data():
    """
    测试真实数据的复权计算
    """
    print("\n" + "=" * 60)
    print("测试真实数据的复权计算")
    print("=" * 60)
    
    try:
        from quant.tdx_data import get_code, GbbqReader
        
        # 获取一只股票的数据进行测试
        print("正在获取测试数据...")
        
        # 获取除权除息数据
        gbbq = GbbqReader().get_data()
        
        # 筛选category=1的数据
        category1_data = gbbq[gbbq['category'] == 1]
        print(f"找到 {len(category1_data)} 条除权除息数据")
        
        if len(category1_data) > 0:
            # 显示前几条数据
            print("\n前5条除权除息数据：")
            for i, (_, row) in enumerate(category1_data.head().iterrows()):
                print(f"  {i+1}. {row['code']} {row['datetime']} "
                      f"分红:{row['hongli_panqianliutong']} "
                      f"配股价:{row['peigujia_qianzongguben']} "
                      f"送股:{row['songgu_qianzongguben']} "
                      f"配股:{row['peigu_houzongguben']}")
        
        # 测试一只股票的复权计算
        test_code = 'sh600000'  # 浦发银行
        print(f"\n测试股票：{test_code}")
        
        # 获取不复权数据
        df_no_adj = get_code(test_code, fq=0)
        print(f"不复权数据：{len(df_no_adj)} 条")
        
        # 获取前复权数据
        df_forward = get_code(test_code, fq=1)
        print(f"前复权数据：{len(df_forward)} 条")
        
        # 获取后复权数据
        df_backward = get_code(test_code, fq=2)
        print(f"后复权数据：{len(df_backward)} 条")
        
        # 比较最近几天的数据
        print(f"\n最近5天数据比较：")
        print("日期        不复权收盘  前复权收盘  后复权收盘")
        print("-" * 50)
        
        for i in range(min(5, len(df_no_adj))):
            idx = -(i+1)  # 从最后一天开始
            no_adj_close = df_no_adj.iloc[idx]['close']
            forward_close = df_forward.iloc[idx]['close']
            backward_close = df_backward.iloc[idx]['close']
            date = df_no_adj.iloc[idx]['datetime']
            
            print(f"{date}  {no_adj_close:8.2f}    {forward_close:8.2f}    {backward_close:8.2f}")
            
    except Exception as e:
        print(f"测试真实数据时出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试复权公式
    test_fuquan_formula()
    
    # 测试真实数据
    test_real_data()
