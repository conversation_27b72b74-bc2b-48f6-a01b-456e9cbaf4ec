from quant.tdx_data import get_data

# 正确的数据，用于测试，不要修改
test_samples = [
    {'code': 'sh688583', 'begin': 20250618, 'end': 20250620, 'nfq': [110.1, 81.98, 76.1], 'qfq': [84.269, 81.98, 76.1], 'hfq': [110.1, 107.109, 99.427]},
]


if __name__ == '__main__':
    code = 'sh688583'
    begin = 20250618
    end = 20250620
    print(f"测试股票: {code}, 日期范围: {begin} - {end}")
    # 测试不复权
    df_no_fq = get_data(code, fq=0, begin=begin, end=end)
    print(f'不复权收盘价: {[round(i, 3) for i in df_no_fq["close"].to_list()]}')
    # 测试后复权
    df_backward = get_data(code, fq=2, begin=begin, end=end)
    print(f'后复权收盘价: {[round(i, 3) for i in df_backward["close"].to_list()]}')
    # 测试前复权
    df_forward = get_data(code, fq=1, begin=begin, end=end)
    print(f'前复权收盘价: {[round(i, 3) for i in df_forward["close"].to_list()]}')



    for sample_dict in test_samples:
        code = sample_dict['code']
        begin = sample_dict['begin']
        end = sample_dict['end']
        df_no_fq = get_data(code, fq=0, begin=begin, end=end)