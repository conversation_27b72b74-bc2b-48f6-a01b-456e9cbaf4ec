#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
get_code 函数使用示例
"""

import sys
sys.path.append('.')

def example_usage():
    """使用示例"""
    from quant.tdx_data import get_code
    
    print("get_code 函数使用示例")
    print("=" * 50)
    
    # 基本用法
    print("1. 基本用法")
    df = get_code('sh600000', fq=0)  # 不复权
    print(f"浦发银行不复权数据：{len(df)} 条")
    
    # 复权数据
    print("\n2. 复权数据")
    df_forward = get_code('sh600000', fq=1)  # 前复权
    df_backward = get_code('sh600000', fq=2)  # 后复权
    print(f"前复权数据：{len(df_forward)} 条")
    print(f"后复权数据：{len(df_backward)} 条")
    
    # 指定日期范围
    print("\n3. 指定日期范围")
    
    # 只指定开始日期
    df_from_2023 = get_code('sh600000', fq=1, begin='20230101')
    print(f"从2023年开始的前复权数据：{len(df_from_2023)} 条")
    
    # 只指定结束日期
    df_until_2022 = get_code('sh600000', fq=1, end=20221231)
    print(f"到2022年结束的前复权数据：{len(df_until_2022)} 条")
    
    # 指定完整日期范围
    df_2022 = get_code('sh600000', fq=1, begin='20220101', end='20221231')
    print(f"2022年全年的前复权数据：{len(df_2022)} 条")
    
    # 显示数据结构
    print("\n4. 数据结构")
    print("列名：", list(df.columns))
    print("索引名：", df.index.name)
    print("\n前5行数据：")
    print(df.head())
    
    print("\n使用示例完成！")

if __name__ == "__main__":
    example_usage()
