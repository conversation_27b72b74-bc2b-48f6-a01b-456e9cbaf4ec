#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证复权计算逻辑
"""

def verify_fuquan_calculation():
    """
    根据用户观察的 sh603194 数据验证复权计算逻辑
    
    用户观察数据：
    日期        不复权    前复权    后复权
    20250613   42.75     42.12     42.75
    20250616   42.19     42.19     42.82
    20250617   42.8      42.8      43.43
    """
    print("=" * 60)
    print("验证 sh603194 复权计算逻辑")
    print("=" * 60)
    
    # 用户观察数据
    data = {
        20250613: {'no_adj': 42.75, 'forward': 42.12, 'backward': 42.75},
        20250616: {'no_adj': 42.19, 'forward': 42.19, 'backward': 42.82},
        20250617: {'no_adj': 42.8, 'forward': 42.8, 'backward': 43.43}
    }
    
    print("用户观察数据：")
    print("日期        不复权    前复权    后复权")
    print("-" * 40)
    for date, prices in data.items():
        print(f"{date}   {prices['no_adj']:6.2f}    {prices['forward']:6.2f}    {prices['backward']:6.2f}")
    
    print("\n分析：")
    
    # 1. 分析派息金额
    dividend_amount = data[20250613]['no_adj'] - data[20250616]['no_adj']
    print(f"1. 推断派息金额：{data[20250613]['no_adj']} - {data[20250616]['no_adj']} = {dividend_amount:.2f} 元/股")
    print(f"   即每10股派息：{dividend_amount * 10:.1f} 元")
    
    # 2. 验证前复权逻辑
    print(f"\n2. 前复权逻辑验证：")
    print(f"   前复权特点：除息日及之后价格不变，除息日之前价格向下调整")
    
    # 除息日及之后：前复权 = 不复权
    print(f"   20250616: 前复权({data[20250616]['forward']}) = 不复权({data[20250616]['no_adj']}) ✓")
    print(f"   20250617: 前复权({data[20250617]['forward']}) = 不复权({data[20250617]['no_adj']}) ✓")
    
    # 计算前复权因子
    forward_factor = data[20250616]['no_adj'] / data[20250613]['no_adj']
    print(f"   前复权因子 = {data[20250616]['no_adj']} / {data[20250613]['no_adj']} = {forward_factor:.6f}")
    
    # 验证除息日之前的前复权价格
    calculated_forward_20250613 = data[20250613]['no_adj'] * forward_factor
    print(f"   20250613 计算的前复权价格 = {data[20250613]['no_adj']} × {forward_factor:.6f} = {calculated_forward_20250613:.2f}")
    print(f"   用户观察的前复权价格 = {data[20250613]['forward']}")
    print(f"   差异 = {abs(calculated_forward_20250613 - data[20250613]['forward']):.2f}")
    
    # 3. 验证后复权逻辑
    print(f"\n3. 后复权逻辑验证：")
    print(f"   后复权特点：除息日之前价格不变，除息日及之后价格向上调整")
    
    # 除息日之前：后复权 = 不复权
    print(f"   20250613: 后复权({data[20250613]['backward']}) = 不复权({data[20250613]['no_adj']}) ✓")
    
    # 计算后复权因子
    backward_factor = data[20250613]['no_adj'] / data[20250616]['no_adj']
    print(f"   后复权因子 = {data[20250613]['no_adj']} / {data[20250616]['no_adj']} = {backward_factor:.6f}")
    
    # 验证除息日及之后的后复权价格
    calculated_backward_20250616 = data[20250616]['no_adj'] * backward_factor
    calculated_backward_20250617 = data[20250617]['no_adj'] * backward_factor
    
    print(f"   20250616 计算的后复权价格 = {data[20250616]['no_adj']} × {backward_factor:.6f} = {calculated_backward_20250616:.2f}")
    print(f"   用户观察的后复权价格 = {data[20250616]['backward']}")
    print(f"   差异 = {abs(calculated_backward_20250616 - data[20250616]['backward']):.2f}")
    
    print(f"   20250617 计算的后复权价格 = {data[20250617]['no_adj']} × {backward_factor:.6f} = {calculated_backward_20250617:.2f}")
    print(f"   用户观察的后复权价格 = {data[20250617]['backward']}")
    print(f"   差异 = {abs(calculated_backward_20250617 - data[20250617]['backward']):.2f}")
    
    # 4. 验证复权因子关系
    print(f"\n4. 复权因子关系验证：")
    print(f"   前复权因子 × 后复权因子 = {forward_factor:.6f} × {backward_factor:.6f} = {forward_factor * backward_factor:.6f}")
    print(f"   应该等于 1.0，差异：{abs(forward_factor * backward_factor - 1.0):.8f}")
    
    # 5. 标准复权公式验证
    print(f"\n5. 标准复权公式验证：")
    print(f"   假设只有现金分红，无送股配股")
    
    pre_close = data[20250613]['no_adj']  # 除权前收盘价
    cash_dividend = dividend_amount       # 现金红利
    
    # 理论除权价
    theoretical_ex_price = pre_close - cash_dividend
    print(f"   理论除权价 = {pre_close} - {cash_dividend} = {theoretical_ex_price:.2f}")
    print(f"   实际除权日价格 = {data[20250616]['no_adj']}")
    print(f"   差异 = {abs(theoretical_ex_price - data[20250616]['no_adj']):.2f}")
    
    # 理论前复权因子
    theoretical_forward_factor = theoretical_ex_price / pre_close
    print(f"   理论前复权因子 = {theoretical_ex_price:.2f} / {pre_close} = {theoretical_forward_factor:.6f}")
    print(f"   实际前复权因子 = {forward_factor:.6f}")
    print(f"   差异 = {abs(theoretical_forward_factor - forward_factor):.8f}")
    
    # 6. 结论
    print(f"\n6. 结论：")
    if abs(calculated_forward_20250613 - data[20250613]['forward']) < 0.1:
        print("   ✓ 前复权计算基本正确")
    else:
        print("   ✗ 前复权计算存在偏差")
        
    if abs(calculated_backward_20250616 - data[20250616]['backward']) < 0.1:
        print("   ✓ 后复权计算基本正确")
    else:
        print("   ✗ 后复权计算存在偏差")
        
    if abs(forward_factor * backward_factor - 1.0) < 0.001:
        print("   ✓ 前后复权因子关系正确")
    else:
        print("   ✗ 前后复权因子关系异常")

def analyze_code_logic():
    """
    分析代码中的复权计算逻辑
    """
    print("\n" + "=" * 60)
    print("分析代码中的复权计算逻辑")
    print("=" * 60)
    
    print("代码中的关键逻辑：")
    print("1. 前复权：从最新日期向前计算复权因子")
    print("   - 累计因子[i] = 当前因子[i] × 累计因子[i+1]")
    print("   - 前复权价格 = 原始价格 × 累计复权因子")
    
    print("\n2. 后复权：从最早日期向后计算复权因子")
    print("   - 累计因子[i] = 当前因子[i] × 累计因子[i-1]")
    print("   - 后复权价格 = 原始价格 × 累计复权因子")
    
    print("\n3. 除权除息复权因子计算：")
    print("   - 前复权因子 = (除权前收盘价 - 现金红利 + 配股比例 × 配股价) /")
    print("                 ((1 + 送转股比例 + 配股比例) × 除权前收盘价)")
    print("   - 后复权因子 = 前复权因子的倒数")
    
    print("\n4. 修正后的逻辑：")
    print("   - 使用除权日前一天的收盘价作为'除权前收盘价'")
    print("   - 而不是除权日当天的收盘价")

if __name__ == "__main__":
    verify_fuquan_calculation()
    analyze_code_logic()
