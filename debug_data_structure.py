#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据结构
"""

import sys
sys.path.append('.')

def debug_data():
    """调试数据结构"""
    try:
        from quant.tdx_data import _read_tdx_daily_k
        
        print("调试原始数据结构")
        print("=" * 50)
        
        code = 'sh600000'
        print(f"测试股票：{code}")
        
        # 获取原始数据
        df = _read_tdx_daily_k(code)
        print(f"原始数据长度：{len(df)}")
        print(f"列名：{list(df.columns)}")
        print(f"索引名：{df.index.name}")
        print(f"索引类型：{type(df.index)}")
        print(f"前几行数据：")
        print(df.head())
        
        print(f"\n最后几行数据：")
        print(df.tail())
        
    except Exception as e:
        print(f"调试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data()
