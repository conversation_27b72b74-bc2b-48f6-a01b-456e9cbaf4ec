# 复权因子计算逻辑与经济原理详解（修正版）

## 1. 复权的经济原理

### 1.1 复权的本质
复权是为了消除公司行为（如分红、送股、配股等）对股价历史连续性的影响，使得不同时期的股价具有可比性。

**主要复权事件类型**：
1. **除权除息**：分红、送股、配股的综合事件
2. **股本变化**：送配股上市、非流通股上市、股份回购等
3. **增发新股**：公司增发新股上市
4. **扩缩股**：股本按比例扩大或缩小
5. **权证**：送认购权证、送认沽权证

### 1.2 前复权 vs 后复权

**前复权（Forward Adjustment）**：
- **原理**：以当前价格为基准，向前调整历史价格
- **经济意义**：反映当前股本结构下的历史价格水平
- **应用场景**：技术分析、趋势判断
- **特点**：历史价格向下调整，当前价格保持不变

**后复权（Backward Adjustment）**：
- **原理**：以历史价格为基准，向后调整当前和未来价格
- **经济意义**：保持历史价格的真实性，便于理解历史投资回报
- **应用场景**：历史收益分析、投资回报计算
- **特点**：当前价格向上调整，历史价格保持不变

## 2. gbbq数据字段含义（根据category类型）

### 2.1 数据字段解释

根据您提供的gbbq数据解释，四个数据字段在不同category下有不同含义：

**category=1 除权除息**：
- 数据1（hongli_panqianliutong）：分红（每10股派几元）
- 数据2（peigujia_qianzongguben）：配股价
- 数据3（songgu_qianzongguben）：送转股（每10股送几股）
- 数据4（peigu_houzongguben）：配股（每10股配几股）

**category=2,3,5,7,8,9,10 股本变化类**：
- 数据1：前流通盘
- 数据2：前总股本
- 数据3：后流通盘
- 数据4：后总股本

**category=6 增发新股**：
- 数据1：0
- 数据2：增发价
- 数据3：增发数量
- 数据4：0

**category=11,12 扩缩股**：
- 数据1：0
- 数据2：0
- 数据3：比例
- 数据4：0

**category=13,14 权证**：
- 数据1：行权价
- 数据2：0
- 数据3：份数
- 数据4：0

## 3. 复权因子计算逻辑

### 3.1 标准复权因子计算（除权除息）

**标准复权公式（除权除息事件）**：

对于category=1的除权除息事件，使用标准的复权公式：

```
前复权因子 = (除权前收盘价 - 现金红利 + 配股比例 × 配股价) /
           ((1 + 送转股比例 + 配股比例) × 除权前收盘价)

后复权因子 = 前复权因子的倒数
```

**参数说明**：
- 除权前收盘价：当日收盘价
- 现金红利：数据1 / 10（每股现金红利）
- 送转股比例：数据3 / 10（送转股比例）
- 配股比例：数据4 / 10（配股比例）
- 配股价：数据2（配股价格）

### 3.2 股本变化类复权因子

对于category=2,3,5,7,8,9,10的股本变化事件：

```
前复权因子 = 前总股本 / 后总股本
后复权因子 = 后总股本 / 前总股本
```

### 3.3 增发新股复权因子

对于category=6的增发新股事件：

```
前复权因子 = (原股本 × 除权前价格 + 增发股本 × 增发价) /
           ((原股本 + 增发股本) × 除权前价格)

后复权因子 = 前复权因子的倒数
```

### 3.4 扩缩股复权因子

对于category=11,12的扩缩股事件：

```
前复权因子 = 比例（数据3）
后复权因子 = 1 / 比例
```

### 3.5 累计复权因子

**前复权累计因子**：
```
累计因子[i] = 当前因子[i] × 累计因子[i+1]
```
- 从最新日期向前累积计算
- 每个历史价格都乘以从该日期到当前日期的所有复权因子

**后复权累计因子**：
```
累计因子[i] = 当前因子[i] × 累计因子[i-1]
```
- 从最早日期向后累积计算
- 每个当前和未来价格都乘以从最早日期到该日期的所有复权因子

### 3.6 复权价格计算

**前复权价格**：
```
前复权价格 = 原始价格 × 累计复权因子
```

**后复权价格**：
```
后复权价格 = 原始价格 × 累计复权因子
```

## 4. 性能优化策略

### 4.1 向量化操作
- 使用NumPy数组进行批量计算
- 避免逐行循环操作
- 一次性计算所有复权因子

### 4.2 内存优化
- 预分配数组避免动态扩展
- 使用数组切片而不是DataFrame逐行操作
- 减少中间变量创建

### 4.3 算法优化
- 分离复权因子计算和价格应用
- 使用向量化乘法替代逐行乘法
- 缓存重复计算结果

## 5. 复权类型详细说明

### 5.1 公司行为类型（category字段）

| 类别 | 名称 | 数据1 | 数据2 | 数据3 | 数据4 |
|------|------|-------|-------|-------|-------|
| 1 | 除权除息 | 分红（每10股派几元） | 配股价 | 送转股（每10股送几股） | 配股（每10股配几股） |
| 2 | 送配股上市 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 3 | 非流通股上市 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 4 | 未知股本变动 | 0 | 0 | 0 | 0 |
| 5 | 股本变化 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 6 | 增发新股 | 0 | 增发价 | 增发数量 | 0 |
| 7 | 股份回购 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 8 | 增发新股上市 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 9 | 转配股上市 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 10 | 可转债上市 | 前流通盘 | 前总股本 | 后流通盘 | 后总股本 |
| 11 | 扩缩股 | 0 | 0 | 比例 | 0 |
| 12 | 非流通股缩股 | 0 | 0 | 比例 | 0 |
| 13 | 送认购权证 | 行权价 | 0 | 份数 | 0 |
| 14 | 送认沽权证 | 行权价 | 0 | 份数 | 0 |

### 5.2 复权参数字段说明
- `hongli_panqianliutong`：数据1，根据category类型有不同含义
- `peigujia_qianzongguben`：数据2，根据category类型有不同含义
- `songgu_qianzongguben`：数据3，根据category类型有不同含义
- `peigu_houzongguben`：数据4，根据category类型有不同含义

## 6. 复权计算示例

### 6.1 除权除息计算示例

假设某股票除权除息事件：
- 除权前收盘价：10元
- 每10股派红利：2元
- 每10股送股：3股
- 每10股配股：2股
- 配股价：5元

**计算过程**：
```
现金红利 = 2/10 = 0.2元/股
送转股比例 = 3/10 = 0.3
配股比例 = 2/10 = 0.2
配股价 = 5元

分子 = 10 - 0.2 + 0.2 × 5 = 10.8
分母 = (1 + 0.3 + 0.2) × 10 = 15

前复权因子 = 10.8 / 15 = 0.72
后复权因子 = 15 / 10.8 = 1.3889

除权后理论价格 = 10.8 / 1.5 = 7.2元
```

### 6.2 使用示例

```python
# 获取前复权数据
df_forward = get_code('sh000001', fq=1)

# 获取后复权数据
df_backward = get_code('sh000001', fq=2)

# 获取不复权数据
df_raw = get_code('sh000001', fq=0)

# 验证复权计算
from quant.test_adjustment import test_adjustment_formula
test_adjustment_formula()
```

## 7. 性能基准

修正后的实现特点：
- **计算准确性**：根据gbbq数据解释正确处理不同复权事件
- **计算速度**：保持向量化操作的性能优势
- **内存使用**：优化的数组操作减少内存占用
- **代码可读性**：清晰的分类处理逻辑
- **维护性**：更好的模块化设计

## 8. 注意事项

1. **数据完整性**：确保复权数据与K线数据的时间对齐
2. **精度问题**：使用浮点数计算时注意精度损失
3. **边界条件**：处理没有复权数据的股票或异常数据
4. **复权事件分类**：正确识别和处理不同category类型的事件
5. **性能考虑**：大数据量时的内存使用和计算效率
6. **缓存策略**：合理使用缓存提高重复查询性能
7. **数据验证**：定期验证复权计算结果的正确性

## 9. 修正要点总结

1. **字段含义修正**：根据category类型正确解释四个数据字段
2. **公式修正**：使用标准的复权公式计算除权除息
3. **分类处理**：不同复权事件类型采用不同的计算方法
4. **前后复权关系**：确保前复权和后复权因子互为倒数
5. **边界处理**：增加异常情况的处理逻辑