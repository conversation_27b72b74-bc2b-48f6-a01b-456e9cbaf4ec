#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试复权计算
"""

import sys
sys.path.append('.')

def test_simple():
    """简单测试"""
    try:
        from quant.tdx_data import get_code
        
        print("测试复权计算（只处理Category=1除权除息）")
        print("=" * 50)
        
        # 测试一只股票
        code = 'sh600000'
        print(f"测试股票：{code}")
        
        # 获取不同复权类型的数据
        df0 = get_code(code, fq=0)  # 不复权
        df1 = get_code(code, fq=1)  # 前复权
        df2 = get_code(code, fq=2)  # 后复权
        
        print(f"数据长度 - 不复权:{len(df0)}, 前复权:{len(df1)}, 后复权:{len(df2)}")
        
        # 显示最近几天的数据
        print("\n最近3天数据对比:")
        print("日期        不复权    前复权    后复权")
        print("-" * 40)
        
        for i in range(min(3, len(df0))):
            idx = -(i+1)
            date = df0.iloc[idx]['datetime']
            close0 = df0.iloc[idx]['close']
            close1 = df1.iloc[idx]['close']
            close2 = df2.iloc[idx]['close']
            print(f"{date}  {close0:7.2f}  {close1:7.2f}  {close2:7.2f}")
            
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple()
