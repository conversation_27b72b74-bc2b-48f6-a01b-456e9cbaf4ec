"""
辅助性函数
"""
from quant.config import CONFIG
import datetime
import bisect

def round_price(price: float, precision: int=3) -> float:
    """
    四舍五入
    """
    return int(price * 10 ** precision + 0.5) / 10 ** precision

def _limit_price(pre_close: float, up:bool =True) -> float:
    """
    获取涨停价或跌停价
    """
    p = pre_close * 1.1 if up else pre_close * 0.9
    # 四舍五入
    return round_price(p)

def limit_up_price(pre_close: float) -> float:
    """
    获取涨停价
    """
    return _limit_price(pre_close, True)

def limit_down_price(pre_close: float) -> float:
    """
    获取跌停价
    """
    return _limit_price(pre_close, False)

def _std_date_str(date: str | datetime.date=None) -> str:
    """
    将日期字符串格式化为 yyyy-mm-dd
    """
    if date is None:
        date = datetime.datetime.now().strftime('%Y-%m-%d')
    
    if isinstance(date, str):
        if '-' not in date:
            date = f'{date[:4]}-{date[4:6]}-{date[6:]}'
    elif isinstance(date, datetime.date):
        date = date.strftime('%Y-%m-%d')
    else:
        raise ValueError(f'Invalid date type: {type(date)}')

    return date

def is_trade_date(date: str | datetime.date=None) -> bool:
    """
    判断是否为交易日
    """
    if date is None:
        date = datetime.datetime.now().strftime('%Y-%m-%d')

    date = _std_date_str(date)
    return date in CONFIG.TRADE_DATES

def pre_trade_date(date: str | datetime.date = None) -> str:
    """
    获取前一个交易日
    若给定日期(默认当前日期)为非交易日,则返回给定日期的前一个交易日
    """
    if date is None:
        date = datetime.datetime.now().strftime('%Y-%m-%d')

    date = _std_date_str(date)
    
    # 使用二分查找优化性能
    dates = CONFIG.TRADE_DATES
    idx = bisect.bisect_left(dates, date)
    
    if idx == 0:
        raise ValueError(f'No trading date before {date}')
    
    # 如果 date 在 dates 中，返回前一个交易日
    if idx < len(dates) and dates[idx] == date:
        return dates[idx - 1]
    
    # 如果 date 不在 dates 中，返回前一个交易日
    return dates[idx - 1]

def next_trade_date(date: str | datetime.date = None) -> str:
    """
    获取下一个交易日
    若给定日期(默认当前日期)为非交易日,则返回给定日期的下一个交易日
    """
    if date is None:
        date = datetime.datetime.now().strftime('%Y-%m-%d')

    date = _std_date_str(date)
    
    # 使用二分查找优化性能
    dates = CONFIG.TRADE_DATES
    idx = bisect.bisect_right(dates, date)
    
    if idx == len(dates):
        raise ValueError(f'No trading date after {date}')
    
    # 如果 date 在 dates 中，返回下一个交易日
    if idx > 0 and dates[idx - 1] == date:
        return dates[idx]
    
    # 如果 date 不在 dates 中，返回下一个交易日
    return dates[idx]

if __name__ == '__main__':
    print('测试is_trade_date')
    print('当天是交易日吗？', is_trade_date())
    assert is_trade_date('2025-01-24')
    assert not is_trade_date('2025-01-28')

    print('测试pre_trade_date')
    assert pre_trade_date('2025-01-24') == '2025-01-23'
    assert pre_trade_date('2025-01-26') == '2025-01-24'
    assert pre_trade_date('2025-01-27') == '2025-01-24'

    print('测试next_trade_date')
    assert next_trade_date('2025-01-23') == '2025-01-24'
    assert next_trade_date('2025-01-24') == '2025-01-27'
    assert next_trade_date('2025-01-26') == '2025-01-27'


