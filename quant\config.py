
import os, datetime
import pandas as pd
import akshare as ak

def singleton(cls):
    instances = {}
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    return get_instance

# 获取当前文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 获取当前文件所在目录的绝对路径
directory_path = os.path.dirname(current_file_path)

def _read_funds(_type: str):
    datas = {}
    with open(os.path.join(directory_path, f'{_type}_codes.txt'), 'r', encoding='utf-8') as f:
        d = f.read().splitlines()
    d = [i.split(' ') for i in d]
    for i in d:
        datas[i[0]] = i[1]
    return datas

def _read_etfs():
    return _read_funds('etf')

def _read_lofs():
    return _read_funds('lof')

def init_trade_dates():
    dates = pd.read_csv(os.path.join(directory_path, f'trade_dates.csv'))
    # 获取当前日期
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    
    # 如果当前日期在最后5个交易日内
    if today in dates['trade_date'].values[-5:]:
        # 重新获取交易日历
        new_dates = ak.tool_trade_date_hist_sina()
        # 保存到csv
        new_dates.to_csv(os.path.join(directory_path, 'trade_dates.csv'), index=False)
        dates = new_dates
        
    return dates['trade_date'].values.tolist()

@singleton
class Config:
    def __init__(self):
        self.ETFS = _read_etfs()
        self.LOFS = _read_lofs()
        self.TRADE_DATES = init_trade_dates()

CONFIG = Config()