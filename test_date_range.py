#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期范围功能
"""

import sys
sys.path.append('.')

def test_date_range():
    """测试日期范围功能"""
    try:
        from quant.tdx_data import get_code
        
        print("测试日期范围功能")
        print("=" * 50)
        
        code = 'sh600000'
        print(f"测试股票：{code}")
        
        # 测试1：获取全部数据
        print("\n1. 获取全部数据")
        df_all = get_code(code, fq=0)
        print(f"全部数据：{len(df_all)} 条")
        if len(df_all) > 0:
            # 检查日期是在索引还是在列中
            if 'datetime' in df_all.columns:
                start_date = df_all.iloc[0]['datetime']
                end_date = df_all.iloc[-1]['datetime']
            else:
                # 日期在索引中
                start_date = df_all.index[0].strftime('%Y%m%d')
                end_date = df_all.index[-1].strftime('%Y%m%d')
            print(f"日期范围：{start_date} ~ {end_date}")
        
        def get_date_range(df):
            """获取DataFrame的日期范围"""
            if len(df) == 0:
                return "无数据", "无数据"
            if 'datetime' in df.columns:
                return df.iloc[0]['datetime'], df.iloc[-1]['datetime']
            else:
                return df.index[0].strftime('%Y%m%d'), df.index[-1].strftime('%Y%m%d')

        # 测试2：指定开始日期
        print("\n2. 指定开始日期 (begin='20230101')")
        df_begin = get_code(code, fq=0, begin='20230101')
        print(f"从2023年开始：{len(df_begin)} 条")
        if len(df_begin) > 0:
            start_date, end_date = get_date_range(df_begin)
            print(f"日期范围：{start_date} ~ {end_date}")

        # 测试3：指定结束日期
        print("\n3. 指定结束日期 (end=20221231)")
        df_end = get_code(code, fq=0, end=20221231)
        print(f"到2022年结束：{len(df_end)} 条")
        if len(df_end) > 0:
            start_date, end_date = get_date_range(df_end)
            print(f"日期范围：{start_date} ~ {end_date}")

        # 测试4：指定日期范围
        print("\n4. 指定日期范围 (begin='20220101', end='20221231')")
        df_range = get_code(code, fq=0, begin='20220101', end='20221231')
        print(f"2022年全年：{len(df_range)} 条")
        if len(df_range) > 0:
            start_date, end_date = get_date_range(df_range)
            print(f"日期范围：{start_date} ~ {end_date}")

        # 测试5：测试复权数据的日期范围
        print("\n5. 测试前复权数据的日期范围")
        df_fq = get_code(code, fq=1, begin='20230101', end='20231231')
        print(f"2023年前复权：{len(df_fq)} 条")
        if len(df_fq) > 0:
            start_date, end_date = get_date_range(df_fq)
            print(f"日期范围：{start_date} ~ {end_date}")
            print(f"前几天收盘价：")
            for i in range(min(3, len(df_fq))):
                row = df_fq.iloc[i]
                if 'datetime' in df_fq.columns:
                    date_str = row['datetime']
                else:
                    date_str = df_fq.index[i].strftime('%Y%m%d')
                print(f"  {date_str}: {row['close']:.2f}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_date_range()
